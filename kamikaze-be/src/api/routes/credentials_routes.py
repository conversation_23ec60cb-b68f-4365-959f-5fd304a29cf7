"""
Exchange Credentials API Routes
Provides REST API endpoints for managing exchange API credentials
"""

import logging
from typing import Any, Dict, Optional, Union

from fastapi import APIRouter, Depends, HTTPException, Request
from pydantic import BaseModel, Field

from ...infrastructure.credentials_database import credentials_db
from ...services.binance_connection_service import binance_service
from .auth_routes import get_current_user

# Setup logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/api/v1/credentials", tags=["Exchange Credentials"])

# ============================================================================
# Pydantic Models
# ============================================================================



class BinanceCredentialsRequest(BaseModel):
    """Request model for Binance credentials."""

    api_key: str = Field(..., description="Binance API key")
    secret_key: str = Field(..., description="Binance secret key")


class ConnectionTestRequest(BaseModel):
    """Request model for connection testing."""

    api_key: str = Field(..., description="API key")
    secret_key: str = Field(..., description="Secret key")


class CredentialsResponse(BaseModel):
    """Response model for credentials operations."""

    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None


class ConnectionTestResponse(BaseModel):
    """Response model for connection test."""

    success: bool
    message: str
    account_info: Optional[Dict[str, Any]] = None
    permissions: Optional[list] = None
    environment: Optional[str] = None
    error_code: Optional[Union[str, int]] = None



# ============================================================================
# Binance Credentials Routes
# ============================================================================


@router.post("/binance", response_model=CredentialsResponse)
async def save_binance_credentials(
    request: BinanceCredentialsRequest,
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """Save Binance credentials for the authenticated user."""
    try:
        if not await credentials_db.ensure_connected():
            raise HTTPException(
                status_code=503, detail="Database service not available"
            )

        user_id = current_user["id"]

        # Test connection before saving (mainnet only)
        async with binance_service as service:
            test_result = await service.test_connection(
                request.api_key, request.secret_key, is_testnet=False
            )

            if not test_result["success"]:
                return CredentialsResponse(
                    success=False,
                    message=f"Connection test failed: {test_result['message']}",
                    data={"error_code": test_result.get("error_code")},
                )

        # Save credentials (mainnet only)
        success = await credentials_db.save_binance_credentials(
            user_id, request.api_key, request.secret_key, is_mainnet=True
        )

        if success:
            return CredentialsResponse(
                success=True,
                message="Binance mainnet credentials saved successfully",
                data={
                    "environment": "mainnet",
                    "is_mainnet": True,
                    "account_info": test_result.get("account_info"),
                },
            )
        else:
            return CredentialsResponse(
                success=False, message="Failed to save Binance credentials"
            )

    except Exception as e:
        logger.error(f"Error saving Binance credentials: {e}")
        return CredentialsResponse(
            success=False, message="An error occurred while saving credentials"
        )


@router.get("/binance", response_model=CredentialsResponse)
async def get_binance_credentials(
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """Get Binance credentials status for the authenticated user."""
    try:
        if not await credentials_db.ensure_connected():
            raise HTTPException(
                status_code=503, detail="Database service not available"
            )

        user_id = current_user["id"]
        credentials = await credentials_db.get_user_binance_credentials(user_id)

        # Return status without sensitive data - mainnet only
        has_mainnet = credentials["mainnet"] is not None

        status = {
            "mainnet": {
                "configured": has_mainnet,
                "created_at": (
                    credentials["mainnet"]["created_at"]
                    if credentials["mainnet"]
                    else None
                ),
                "updated_at": (
                    credentials["mainnet"]["updated_at"]
                    if credentials["mainnet"]
                    else None
                ),
            },
        }

        return CredentialsResponse(
            success=True,
            message="Binance credentials status retrieved successfully",
            data={
                "status": status,
                "has_credentials": has_mainnet,
                "mainnet": has_mainnet,
                "preferred_environment": "mainnet" if has_mainnet else None,
            },
        )

    except Exception as e:
        logger.error(f"Error getting Binance credentials: {e}")
        return CredentialsResponse(
            success=False, message="An error occurred while retrieving credentials"
        )


# ============================================================================
# Connection Testing Routes
# ============================================================================


@router.post("/test-connection", response_model=ConnectionTestResponse)
async def test_connection(
    request: ConnectionTestRequest,
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """Test connection to exchange with provided credentials."""
    try:
        async with binance_service as service:
            result = await service.test_connection(
                request.api_key, request.secret_key, is_testnet=False
            )

            # Add detailed error information for debugging
            response = ConnectionTestResponse(
                success=result["success"],
                message=result["message"],
                account_info=result.get("account_info"),
                permissions=result.get("permissions"),
                environment=result.get("environment"),
                error_code=result.get("error_code"),
            )

            # Log detailed error information for debugging
            if not result["success"]:
                logger.error(f"❌ Connection test failed for user {current_user['id']}")
                logger.error(f"   Error: {result.get('error')}")
                logger.error(f"   Error Code: {result.get('error_code')}")
                logger.error(f"   Message: {result.get('message')}")
                logger.error(f"   Environment: {result.get('environment')}")
                logger.error(f"   Full result: {result}")
            else:
                logger.info(
                    f"✅ Connection test succeeded for user {current_user['id']}"
                )
                logger.info(f"   Environment: {result.get('environment')}")
                logger.info(
                    f"   Account info: {result.get('account_info', {}).get('accountType', 'N/A')}"
                )

            return response

    except Exception as e:
        logger.error(f"Connection test error: {e}")
        return ConnectionTestResponse(
            success=False, message="Connection test failed", error_code="TEST_ERROR"
        )


@router.post("/debug-test-connection", response_model=ConnectionTestResponse)
async def debug_test_connection(request: ConnectionTestRequest):
    """Debug endpoint to test connection without authentication (for troubleshooting)."""
    try:
        logger.info(f"🔧 Debug connection test for API key: {request.api_key[:8]}...")

        async with binance_service as service:
            result = await service.test_connection(
                request.api_key, request.secret_key, is_testnet=False
            )

            logger.info(f"🔧 Debug test result: {result}")

            # Add detailed error information for debugging
            response = ConnectionTestResponse(
                success=result["success"],
                message=result["message"],
                account_info=result.get("account_info"),
                permissions=result.get("permissions"),
                environment=result.get("environment"),
                error_code=result.get("error_code"),
            )

            # Log the response for debugging
            logger.info(f"🔧 Debug response: {response}")
            return response

    except Exception as e:
        logger.error(f"Debug connection test error: {e}")
        import traceback
        traceback.print_exc()
        return ConnectionTestResponse(
            success=False,
            message=f"Debug connection test failed: {str(e)}",
            error_code="DEBUG_TEST_ERROR"
        )


@router.post("/validate/{credential_type}")
async def validate_saved_credentials(
    credential_type: str,
    is_mainnet: bool = True,
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """Validate saved credentials by testing connection."""
    try:
        if not await credentials_db.ensure_connected():
            raise HTTPException(
                status_code=503, detail="Database service not available"
            )

        user_id = current_user["id"]

        # Get credentials based on type (mainnet only)
        if credential_type == "binance":
            creds = await credentials_db.get_binance_credentials(user_id, is_mainnet=True)
        elif credential_type == "testnet":
            return CredentialsResponse(
                success=False, message="Testnet credentials are no longer supported"
            )
        else:
            raise HTTPException(status_code=400, detail="Invalid credential type")

        if not creds:
            return CredentialsResponse(
                success=False, message=f"No {credential_type} credentials found"
            )

        # Test connection (mainnet only)
        async with binance_service as service:
            result = await service.test_connection(
                creds["api_key"], creds["secret_key"], is_testnet=False
            )

            return CredentialsResponse(
                success=result["success"],
                message=result["message"],
                data={
                    "environment": result.get("environment"),
                    "account_info": result.get("account_info"),
                    "permissions": result.get("permissions"),
                    "error_code": result.get("error_code"),
                },
            )

    except Exception as e:
        logger.error(f"Credential validation error: {e}")
        return CredentialsResponse(
            success=False, message="Credential validation failed"
        )


# ============================================================================
# Credential Management Routes
# ============================================================================


@router.delete("/binance")
async def delete_binance_credentials(
    is_mainnet: Optional[bool] = None,
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """Delete Binance credentials for the authenticated user."""
    try:
        if not await credentials_db.ensure_connected():
            raise HTTPException(
                status_code=503, detail="Database service not available"
            )

        user_id = current_user["id"]

        success = await credentials_db.delete_credentials(
            user_id, "binance", is_mainnet=is_mainnet
        )

        if success:
            env_desc = "all" if is_mainnet is None else "mainnet"
            return CredentialsResponse(
                success=True,
                message=f"Binance {env_desc} credentials deleted successfully",
            )
        else:
            return CredentialsResponse(
                success=False, message="Failed to delete credentials"
            )

    except Exception as e:
        logger.error(f"Error deleting Binance credentials: {e}")
        return CredentialsResponse(
            success=False, message="An error occurred while deleting credentials"
        )


