#!/usr/bin/env python3
"""
Comprehensive Binance Connection Diagnosis Script
This script will help identify the root cause of connection issues
"""

import sys
import os
import json
import subprocess
import time

# Add the backend source to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'kamikaze-be', 'src'))

def check_python_environment():
    """Check Python environment and required packages."""
    print("🐍 Python Environment Check")
    print("=" * 40)
    print(f"Python version: {sys.version}")
    print(f"Python executable: {sys.executable}")
    
    required_packages = ['aiohttp', 'certifi', 'asyncpg', 'cryptography']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}: Available")
        except ImportError:
            print(f"❌ {package}: Missing")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n🔧 Install missing packages: pip install {' '.join(missing_packages)}")
        return False
    return True

def check_network_connectivity():
    """Check basic network connectivity to Binance."""
    print("\n🌐 Network Connectivity Check")
    print("=" * 40)
    
    try:
        import urllib.request
        import urllib.error
        
        # Test basic connectivity
        url = "https://api.binance.com/api/v3/time"
        print(f"Testing: {url}")
        
        with urllib.request.urlopen(url, timeout=10) as response:
            data = json.loads(response.read().decode())
            print(f"✅ Binance API reachable")
            print(f"📊 Server time: {data.get('serverTime')}")
            return True
            
    except Exception as e:
        print(f"❌ Network connectivity failed: {e}")
        return False

def test_binance_credentials():
    """Test the provided Binance credentials."""
    print("\n🔐 Binance Credentials Test")
    print("=" * 40)
    
    API_KEY = "bsfOd37ZIOtAg4pdhCCUqcDg85fQ1nyjNbZKpIeIDNIqA4ZtrBtvc42FfblGtCyv"
    SECRET_KEY = "cbTLoab9UZNJf1UXhihdxHcFgXUNDilz1YR6gl98w00Ux1mbahcz45uGKpIOSrGK"
    
    print(f"API Key: {API_KEY[:8]}...")
    print(f"Secret Key: {SECRET_KEY[:8]}...")
    
    try:
        import hashlib
        import hmac
        import urllib.request
        import urllib.error
        from urllib.parse import urlencode
        
        # Get server time first
        with urllib.request.urlopen("https://api.binance.com/api/v3/time", timeout=10) as response:
            server_data = json.loads(response.read().decode())
            server_time = server_data['serverTime']
        
        # Test authenticated request
        params = {"timestamp": server_time}
        query_string = urlencode(params)
        signature = hmac.new(
            SECRET_KEY.encode('utf-8'), 
            query_string.encode('utf-8'), 
            hashlib.sha256
        ).hexdigest()
        params["signature"] = signature
        
        url = f"https://api.binance.com/api/v3/account?" + urlencode(params)
        req = urllib.request.Request(url)
        req.add_header('X-MBX-APIKEY', API_KEY)
        
        with urllib.request.urlopen(req, timeout=10) as response:
            data = json.loads(response.read().decode())
            print("✅ Credentials are valid!")
            print(f"📋 Account type: {data.get('accountType', 'Unknown')}")
            print(f"🔄 Can trade: {data.get('canTrade', False)}")
            return True
            
    except urllib.error.HTTPError as e:
        error_data = json.loads(e.read().decode())
        print(f"❌ Authentication failed: {e.code}")
        print(f"📋 Error: {error_data}")
        
        error_code = error_data.get('code')
        if error_code == -2015:
            print("\n🔧 SOLUTION: Check API key permissions and IP restrictions")
        elif error_code == -1021:
            print("\n🔧 SOLUTION: Check system clock synchronization")
        elif error_code == -1022:
            print("\n🔧 SOLUTION: Check secret key")
        
        return False
    except Exception as e:
        print(f"❌ Credentials test failed: {e}")
        return False

def check_backend_service():
    """Check if the backend service can import and run."""
    print("\n🚀 Backend Service Check")
    print("=" * 40)
    
    try:
        from services.binance_connection_service import BinanceConnectionService
        print("✅ BinanceConnectionService imported successfully")
        
        # Test async context manager
        import asyncio
        
        async def test_service():
            try:
                async with BinanceConnectionService() as service:
                    print("✅ Service context manager works")
                    return True
            except Exception as e:
                print(f"❌ Service context manager failed: {e}")
                return False
        
        result = asyncio.run(test_service())
        return result
        
    except ImportError as e:
        print(f"❌ Failed to import BinanceConnectionService: {e}")
        return False
    except Exception as e:
        print(f"❌ Backend service check failed: {e}")
        return False

def check_database_connection():
    """Check database connectivity."""
    print("\n🗄️ Database Connection Check")
    print("=" * 40)
    
    try:
        from infrastructure.database_config import DatabaseConfig
        config = DatabaseConfig()
        print(f"📊 Database host: {config.host}")
        print(f"📊 Database port: {config.port}")
        print(f"📊 Database name: {config.database}")
        
        # Try to connect
        import asyncio
        import asyncpg
        
        async def test_db():
            try:
                conn = await asyncpg.connect(**config.connection_params, timeout=5)
                await conn.close()
                print("✅ Database connection successful")
                return True
            except Exception as e:
                print(f"❌ Database connection failed: {e}")
                return False
        
        return asyncio.run(test_db())
        
    except Exception as e:
        print(f"❌ Database check failed: {e}")
        return False

def main():
    """Run comprehensive diagnosis."""
    print("🔍 Kamikaze Binance Connection Diagnosis")
    print("=" * 50)
    
    checks = [
        ("Python Environment", check_python_environment),
        ("Network Connectivity", check_network_connectivity),
        ("Binance Credentials", test_binance_credentials),
        ("Backend Service", check_backend_service),
        ("Database Connection", check_database_connection),
    ]
    
    results = {}
    for name, check_func in checks:
        try:
            results[name] = check_func()
        except Exception as e:
            print(f"❌ {name} check crashed: {e}")
            results[name] = False
    
    print("\n" + "=" * 50)
    print("📋 DIAGNOSIS SUMMARY")
    print("=" * 50)
    
    for name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {name}")
    
    if all(results.values()):
        print("\n🎉 All checks passed! The issue might be elsewhere.")
    else:
        print("\n🔧 Issues found. Please address the failed checks above.")
    
    return all(results.values())

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
