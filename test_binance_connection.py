#!/usr/bin/env python3
"""
Simple test script to diagnose Binance connection issues
"""

import asyncio
import hashlib
import hmac
import time
import ssl
import json
from urllib.parse import urlencode

try:
    import aiohttp
    import certifi
    AIOHTTP_AVAILABLE = True
except ImportError:
    AIOHTTP_AVAILABLE = False
    print("❌ aiohttp not available, trying with requests")

try:
    import requests
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False

# Test credentials
API_KEY = "bsfOd37ZIOtAg4pdhCCUqcDg85fQ1nyjNbZKpIeIDNIqA4ZtrBtvc42FfblGtCyv"
SECRET_KEY = "cbTLoab9UZNJf1UXhihdxHcFgXUNDilz1YR6gl98w00Ux1mbahcz45uGKpIOSrGK"

BASE_URL = "https://api.binance.com"

def generate_signature(query_string: str, secret_key: str) -> str:
    """Generate HMAC SHA256 signature for Binance API."""
    return hmac.new(
        secret_key.encode("utf-8"), 
        query_string.encode("utf-8"), 
        hashlib.sha256
    ).hexdigest()

def test_server_time_sync():
    """Test basic connectivity to Binance servers using requests."""
    print("🔍 Testing server connectivity...")

    if not REQUESTS_AVAILABLE:
        print("❌ Neither aiohttp nor requests available")
        return False, "No HTTP library available"

    try:
        url = f"{BASE_URL}/api/v3/time"
        print(f"📡 Making request to: {url}")

        response = requests.get(url, timeout=30, headers={
            "User-Agent": "Kamikaze-Trader/1.0",
            "Content-Type": "application/json",
        })

        data = response.json()
        print(f"✅ Server time response: {response.status_code}")
        print(f"📊 Data: {data}")
        return response.status_code == 200, data

    except Exception as e:
        print(f"❌ Server time test failed: {e}")
        return False, str(e)

async def test_server_time():
    """Test basic connectivity to Binance servers."""
    if not AIOHTTP_AVAILABLE:
        return test_server_time_sync()

    print("🔍 Testing server connectivity...")

    ssl_context = ssl.create_default_context(cafile=certifi.where())
    connector = aiohttp.TCPConnector(ssl=ssl_context)

    async with aiohttp.ClientSession(
        timeout=aiohttp.ClientTimeout(total=30),
        connector=connector,
        headers={
            "User-Agent": "Kamikaze-Trader/1.0",
            "Content-Type": "application/json",
        },
    ) as session:
        try:
            url = f"{BASE_URL}/api/v3/time"
            print(f"📡 Making request to: {url}")

            async with session.get(url) as response:
                data = await response.json()
                print(f"✅ Server time response: {response.status}")
                print(f"📊 Data: {data}")
                return response.status == 200, data

        except Exception as e:
            print(f"❌ Server time test failed: {e}")
            return False, str(e)

def test_account_info_sync():
    """Test authenticated request to get account info using requests."""
    print("\n🔐 Testing authenticated request...")

    if not REQUESTS_AVAILABLE:
        return False, "No HTTP library available"

    try:
        # Prepare signed request
        params = {"timestamp": int(time.time() * 1000)}
        query_string = urlencode(params)
        signature = generate_signature(query_string, SECRET_KEY)
        params["signature"] = signature

        headers = {"X-MBX-APIKEY": API_KEY}
        url = f"{BASE_URL}/api/v3/account"

        print(f"📡 Making authenticated request to: {url}")
        print(f"🔑 API Key (first 8 chars): {API_KEY[:8]}...")
        print(f"📝 Query string: {query_string}")
        print(f"🔐 Signature: {signature[:16]}...")

        response = requests.get(url, params=params, headers=headers, timeout=30)
        data = response.json()
        print(f"📊 Response status: {response.status_code}")
        print(f"📋 Response data: {data}")

        if response.status_code == 200:
            print("✅ Authentication successful!")
            return True, data
        else:
            print(f"❌ Authentication failed: {data.get('msg', 'Unknown error')}")
            return False, data

    except Exception as e:
        print(f"❌ Account info test failed: {e}")
        import traceback
        traceback.print_exc()
        return False, str(e)

async def test_account_info():
    """Test authenticated request to get account info."""
    if not AIOHTTP_AVAILABLE:
        return test_account_info_sync()

    print("\n🔐 Testing authenticated request...")

    ssl_context = ssl.create_default_context(cafile=certifi.where())
    connector = aiohttp.TCPConnector(ssl=ssl_context)

    async with aiohttp.ClientSession(
        timeout=aiohttp.ClientTimeout(total=30),
        connector=connector,
        headers={
            "User-Agent": "Kamikaze-Trader/1.0",
            "Content-Type": "application/json",
        },
    ) as session:
        try:
            # Prepare signed request
            params = {"timestamp": int(time.time() * 1000)}
            query_string = urlencode(params)
            signature = generate_signature(query_string, SECRET_KEY)
            params["signature"] = signature

            headers = {"X-MBX-APIKEY": API_KEY}
            url = f"{BASE_URL}/api/v3/account"

            print(f"📡 Making authenticated request to: {url}")
            print(f"🔑 API Key (first 8 chars): {API_KEY[:8]}...")
            print(f"📝 Query string: {query_string}")
            print(f"🔐 Signature: {signature[:16]}...")

            async with session.get(url, params=params, headers=headers) as response:
                data = await response.json()
                print(f"📊 Response status: {response.status}")
                print(f"📋 Response data: {data}")

                if response.status == 200:
                    print("✅ Authentication successful!")
                    return True, data
                else:
                    print(f"❌ Authentication failed: {data.get('msg', 'Unknown error')}")
                    return False, data

        except Exception as e:
            print(f"❌ Account info test failed: {e}")
            import traceback
            traceback.print_exc()
            return False, str(e)

async def main():
    """Run all connection tests."""
    print("🚀 Starting Binance Connection Diagnostics")
    print("=" * 50)
    
    # Test 1: Server connectivity
    server_ok, server_data = await test_server_time()
    if not server_ok:
        print("❌ Cannot connect to Binance servers. Check internet connection.")
        return
    
    # Test 2: Authentication
    auth_ok, auth_data = await test_account_info()
    if not auth_ok:
        print("\n🔧 TROUBLESHOOTING TIPS:")
        if isinstance(auth_data, dict):
            error_code = auth_data.get('code')
            error_msg = auth_data.get('msg', 'Unknown error')
            
            if error_code == -2015:
                print("• Invalid API key or secret")
                print("• Check API key permissions in Binance account")
                print("• Ensure 'Enable Spot & Margin Trading' is checked")
                print("• Verify IP whitelist settings")
            elif error_code == -1021:
                print("• Timestamp out of sync")
                print("• Check system clock")
            elif error_code == -1022:
                print("• Invalid signature")
                print("• Check secret key")
            else:
                print(f"• Error code: {error_code}")
                print(f"• Error message: {error_msg}")
    else:
        print("✅ All tests passed! Connection is working.")
    
    print("\n" + "=" * 50)
    print("🏁 Diagnostics complete")

if __name__ == "__main__":
    asyncio.run(main())
