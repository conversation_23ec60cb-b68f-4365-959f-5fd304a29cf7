#!/usr/bin/env python3
"""
Simple Binance API Test Script
Tests the provided API credentials directly with Binance API
"""

import hashlib
import hmac
import time
import json
from urllib.parse import urlencode

# Test credentials
API_KEY = "bsfOd37ZIOtAg4pdhCCUqcDg85fQ1nyjNbZKpIeIDNIqA4ZtrBtvc42FfblGtCyv"
SECRET_KEY = "cbTLoab9UZNJf1UXhihdxHcFgXUNDilz1YR6gl98w00Ux1mbahcz45uGKpIOSrGK"

BASE_URL = "https://api.binance.com"

def generate_signature(query_string, secret_key):
    """Generate HMAC SHA256 signature for Binance API."""
    return hmac.new(
        secret_key.encode('utf-8'), 
        query_string.encode('utf-8'), 
        hashlib.sha256
    ).hexdigest()

def test_with_urllib():
    """Test using urllib (built-in Python library)."""
    import urllib.request
    import urllib.error
    
    print("🔍 Testing with urllib...")
    
    # Test 1: Server time
    try:
        url = f"{BASE_URL}/api/v3/time"
        print(f"📡 Testing server connectivity: {url}")
        
        with urllib.request.urlopen(url, timeout=10) as response:
            data = json.loads(response.read().decode())
            print(f"✅ Server time: {data}")
            server_time = data['serverTime']
    except Exception as e:
        print(f"❌ Server connectivity failed: {e}")
        return False
    
    # Test 2: Account info (authenticated)
    try:
        # Use server time for better accuracy
        timestamp = server_time
        params = {"timestamp": timestamp}
        query_string = urlencode(params)
        signature = generate_signature(query_string, SECRET_KEY)
        params["signature"] = signature
        
        # Build URL with parameters
        url = f"{BASE_URL}/api/v3/account?" + urlencode(params)
        
        print(f"🔐 Testing authentication...")
        print(f"🔑 API Key: {API_KEY[:8]}...")
        print(f"📝 Timestamp: {timestamp}")
        print(f"🔐 Signature: {signature[:16]}...")
        
        # Create request with headers
        req = urllib.request.Request(url)
        req.add_header('X-MBX-APIKEY', API_KEY)
        req.add_header('User-Agent', 'Kamikaze-Trader/1.0')
        
        with urllib.request.urlopen(req, timeout=10) as response:
            data = json.loads(response.read().decode())
            print(f"✅ Authentication successful!")
            print(f"📋 Account type: {data.get('accountType', 'Unknown')}")
            print(f"🔄 Can trade: {data.get('canTrade', False)}")
            print(f"💰 Balances: {len([b for b in data.get('balances', []) if float(b.get('free', 0)) > 0])} non-zero")
            return True
            
    except urllib.error.HTTPError as e:
        error_data = json.loads(e.read().decode())
        print(f"❌ Authentication failed: {e.code}")
        print(f"📋 Error: {error_data}")
        
        error_code = error_data.get('code')
        error_msg = error_data.get('msg', 'Unknown error')
        
        print(f"\n🔧 TROUBLESHOOTING:")
        if error_code == -2015:
            print("• Invalid API key, IP restriction, or insufficient permissions")
            print("• Check: https://www.binance.com/en/my/settings/api-management")
            print("• Ensure 'Enable Spot & Margin Trading' is checked")
            print("• Verify IP whitelist or use 'Unrestricted' for testing")
        elif error_code == -1021:
            print("• Timestamp out of sync")
            print("• Check system clock synchronization")
        elif error_code == -1022:
            print("• Invalid signature")
            print("• Verify secret key is correct")
        else:
            print(f"• Error code: {error_code}")
            print(f"• Error message: {error_msg}")
        
        return False
        
    except Exception as e:
        print(f"❌ Authentication test failed: {e}")
        return False

def main():
    """Run the Binance API test."""
    print("🚀 Binance API Connection Test")
    print("=" * 50)
    print(f"🔗 Base URL: {BASE_URL}")
    print(f"🔑 API Key: {API_KEY[:8]}...")
    print(f"🔐 Secret Key: {SECRET_KEY[:8]}...")
    print()
    
    success = test_with_urllib()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 SUCCESS: Binance API connection is working!")
        print("✅ Your credentials are valid and properly configured.")
    else:
        print("❌ FAILED: Binance API connection has issues.")
        print("🔧 Please check the troubleshooting tips above.")
    
    print("🏁 Test complete")
    return success

if __name__ == "__main__":
    main()
